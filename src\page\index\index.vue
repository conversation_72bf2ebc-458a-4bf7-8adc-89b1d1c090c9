<template>
  <div class="index" v-show="pageShow">
    <div class="head">
      <div class="user-img">
				<div class="uimg" :style="{backgroundImage:'url('+supPhoto+')', backgroundRepeat:'no-repeat',backgroundPosition: 'center center',backgroundSize: 'cover' }"></div>
      </div>
      <div class="unser-info">
        <div class="user-name">{{supName}}</div>
      </div>
    </div>
    <div class="middle">
      <div class="message" @click="pageTo('historyMessage')">
        <p class="message-title" v-if="messageTitle">
          <span class="message-info-title">消息通知</span>
          <span class="message-info">{{messageTitle}}</span>
          <span class="checkLook">请查看</span>
        </p>
        <p class="message-title" v-else>
          <span class="message-info-title">消息通知</span>
          <span class="message-info">当前暂无相关通知</span>
        </p>
      </div>
      <div class="record">
        <h4 class="record-title">监督员工作/记录</h4>
        <ul class="record-list">
          <!-- 直接进入签到，移除人脸认证入口 -->
          <li class="record-item" @click="pageTo('sign')">
            <img src="../../assets/img/recordImg.png" alt />
          </li>
          <li class="record-item" @click="pageTo('historyRecord')">
            <img src="../../assets/img/historyImg.png" alt />
          </li>
        </ul>
      </div>
      <div class="organ-query">
        <h4 class="organ-title">机构查询</h4>
        <div class="organ-item" @click="pageTo('search')">
          <img src="../../assets/img/organImg.png" alt />
        </div>
      </div>
      <div class="train-material">
        <h4 class="train-title">培训材料</h4>
        <ul class="train-list">
          <li
            class="train-item"
            v-for="item in list"
            :key="item.infoId"
            @click="pageTo('train', item.infoId)"
          >
            <div class="train-book">
              <img src="../../assets/img/trainBook.png" alt />
            </div>
            <div class="train-content">
              <p>{{item.infoTitle}}</p>
              <div class="train-arrow">
                <img src="../../assets/img/leftArrow.png" alt />
              </div>
            </div>
          </li>
        </ul>
      </div>
      <!-- <div class="train-material">
        <h4 class="train-title">本地测试</h4>
        <ul class="train-list">
          <li
            class="train-item"
            @click="test"
          >
            <div class="train-book">
              <img src="../../assets/img/trainBook.png" alt />
            </div>
          </li>
        </ul>
      </div> -->
    </div>
  </div>
</template>
<script>
import { initCookie } from "@/api/check";
// import { getUserIn } from "@/api/sign";
import { getUserInfoByEnc } from "@/api/check";
import { queryTrainingInfo } from "@/api/train";
import { queryMessagePush } from "@/api/history";
// 移除人脸认证相关导入
import userService from "@/services/userService";
import { aesEncrypt } from "@/assets/js/until.js";
export default {
  data() {
    return {
      list: [],
      supName: "",
      supPhoto: "",
      messageTitle: "",
      pageShow: false
    };
  },
  async mounted() {
    this.$loading(true, "");
    await this.initUserInfoFromUrl();
    // this.getInitCookieTest()
  },
  beforeRouteLeave(to, from, next) {
    // console.log(to);
    // console.log(from);
    if (to.name === "IdentityCheck") {
      next(window.yl.call("closeWebview"));
    } else {
      next();
    }
  },
  methods: {
    // 新增：从URL初始化用户信息的方法
    async initUserInfoFromUrl() {
      try {
        console.log('开始初始化用户信息...');

        // 使用简化的用户服务初始化
        const initResult = await userService.initUserInfo();
        console.log('用户信息初始化结果:', initResult);

        //这里使用临时测试手机号不去真的调用接口
        initResult.success = true;
        initResult.userInfo = {
          phone: "18457169126",
        };
        this.userInfo = initResult.userInfo;
        if (initResult.success) {
          // 用户信息获取成功，建立认证状态
          console.log('建立认证状态');
          this.initCookieAfterUserInfo();
          
          this.$loading(false, "");
          this.pageShow = true;
          // 初始化成功后，从URL/存储获取并保存位置信息
          try {
            await userService.getUserLocation();
          } catch (e) {
            console.warn('获取/保存位置信息失败:', e);
          }
          this.getUserInfo();
          this.queryTrainingInfo();
          this.getMessage();
        } else {
          // 初始化失败，显示错误信息并跳转到搜索页面
          console.log('用户信息初始化失败:', initResult.error);
          this.$toast.center(initResult.error || '获取用户信息失败');
          this.$loading(false, "");

          // 跳转到机构查询页面
          this.$router.replace({
            path: "/search"
          });
        }
      } catch (error) {
        console.error('初始化用户信息异常:', error);
        this.$loading(false, "");
        this.$toast.center('系统异常，请稍后重试');

        // 跳转到机构查询页面
        this.$router.replace({
          path: "/search"
        });
      }
    },

    // 新增：用户信息获取成功后建立认证状态
    async initCookieAfterUserInfo() {
      //查看当前是否为开发环境
      console.log('process.env.NODE_ENV==', process.env.NODE_ENV);
      try {
        // 获取用户信息中的手机号
        const userInfo = this.userInfo;
        if (userInfo && userInfo.phone) {
          // 使用AES加密手机号
          const encryptedPhone = aesEncrypt({ phone: userInfo.phone });
          
          // 调用initCookie建立认证状态
          const result = await initCookie({ 
            encoder: encryptedPhone 
          });
          
          if (result.errorCode === '0') {
            console.log('认证状态建立成功');
          } else {
            console.warn('认证状态建立失败:', result);
          }
        } else {
          console.warn('无法获取用户手机号，跳过认证状态建立');
        }
      } catch (error) {
        console.error('建立认证状态异常:', error);
      }
    },

    test(){
      this.$router.push(
        {
          path:'./demo'
        }
      )
    },
    pageTo(url, id) {
      this.$router.push({
        path: url,
        query: {
          id: id
        }
      });
    },
    getInitCookieTest() {
      let that = this;
      initCookie({
        authCode: "11111111"
      })
        .then(res => {
          if (res.errorCode === "0") {
            this.$loading(false, "");
            this.pageShow = true;
            that.getUserInfo();
            that.queryTrainingInfo();
            that.getMessage();
          } else {
            that.$router.replace({
              path: "/search"
            });
          }
        })
        .catch(e => {
          this.$loading(false, "");
          console.log(e);
        });
    },
    getInitCookie() {
      let that = this;
      window.yl.call(
        "getAuthcode",
        {},
        {
          onSuccess: res => {
            console.log(res.param.authCode);
            initCookie({
              authCode: res.param.authCode
            })
              .then(res => {
                console.log('initCookie==', res);
                if (res.errorCode === "0") {
                  that.$loading(false, "");
                  that.pageShow = true;
                  that.getUserInfo();
                  that.queryTrainingInfo();
                  that.getMessage();
                } else {
									console.log("非实名跳转到机构查询页", res);
                  that.$router.replace({
                    path: "/search"
                  });
                }
              })
              .catch(e => {
                that.$loading(false, "");
                console.log(e);
              });
          },
          onFail: res => {
            that.$loading(false, "");
          }
        }
      );
    },
    getUserInfo() {
      console.log('getUserInfo===', this.userInfo);
      getUserInfoByEnc({ phone: this.userInfo.phone })
        .then(res => {
					console.log('getUserInfo===', res);
          if (res.errorCode === "0" || res.errorCode === 0) {
            this.supName = res.data.supName;
            this.supPhoto = res.data.supPhoto;
            this.$loading(false, "");
            // console.log(this.supPhoto);
          } else if (res.errorCode === "1003") {
            console.log("非实名跳转到机构查询页", res);
            this.$router.replace({
              path: "/search"
            });
          } else {
            console.log("eee");
            this.$toast.center(res.val);
          }
        })
        .catch(e => {});
    },
    queryTrainingInfo() {
      queryTrainingInfo({
        pageNum: 1,
        pageSize: 10
      })
        .then(res => {
          console.log(res);
          if (res.errorCode === "0") {
            this.list = res.data;
          } else {
            this.$toast.center(res.value);
          }
        })
        .catch(e => {});
    },
    getMessage() {
      queryMessagePush({
        pageNum: 1,
        pageSize: 1
      }).then(res => {
				console.log('queryMessagePush===', res);
        if (res.errorCode === "0") {
          this.messageTitle = res.data[0] && res.data[0].mesTitle;
          // console.log(res.data[0].mesTitle, this.messageTitle);
        } else if (res.errorCode === "1003") {
          this.getInitCookie();
        }
      });
		},
		getFaceCodeTest() {
			this.$router.push({
				path: "/sign"
			});
		}
    }
 
};
</script>
<style lang="less" scoped>
.index {
  overflow: auto;
  height: 100%;
  -webkit-overflow-scrolling: touch;
}
.head {
  .user-img {
    width: 198px;
    height: 198px;
    margin: 36px auto;
    padding: 30px;
    border-radius: 50%;
    // background:#eee;
    border: 1px solid #4288ff; /*px*/
    overflow: hidden;
    display: flex;
    justify-content: center;
		align-items: center;
		.uimg{
      width: 100%;
      height: 100%;
			border-radius: 50%;
		}
  }
  .unser-info {
    margin-bottom: 57px;
    text-align: center;
    .user-id {
      margin-top: 10px;
    }
    .user-name {
      font-size: 34px;
    }
  }
}
.middle {
  .message {
    width: 695px;
    height: 86px;
    line-height: 86px;
    background: url("../../assets/img/messageBg.png") no-repeat center center;
    background-size: 100%;
    margin: 0 auto;
    // text-align: center;
    .message-title {
      // width:440px;
      font-size: 32px;
      color: #1160e6;
      // border:1px solid #ddd;
      font-weight: bold;
      display: flex;
      margin-left: 38px;
      .message-info-title {
        display: inline-block;
        width: 148px;
      }
      .message-info {
        display: inline-block;
        width: 350px;
        font-size: 30px;
        font-weight: normal;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin-left: 30px;
        text-align: left;
      }
      .checkLook {
        font-size: 30px;
        height: 60px;
        font-weight: normal;
        border-bottom: 1px solid #1160e6;
        margin-left: 10px;
        text-align: center;
        display: inline-block;
      }
    }
  }
  .record {
    margin-top: 60px;
    margin-left: 31px;
    .record-title {
      font-size: 34px;
      color: #000000;
    }
    .record-list {
      display: flex;
      margin-top: 43px;
      .record-item {
        width: 338px;
        height: 176px;
        img {
          width: 100%;
          height: 100%;
        }
      }
      .record-item:first-child {
        margin-right: 18px;
      }
    }
  }
  .organ-query {
    margin-top: 60px;
    margin-left: 31px;
    .organ-title {
      font-size: 34px;
    }
    .organ-item {
      width: 338px;
      height: 176px;
      margin-top: 43px;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
  .train-material {
    margin: 60px 0 0px 31px;
    .train-title {
      font-size: 34px;
    }
    .train-list {
      margin-top: 41px;
      .train-item {
        display: flex;
        align-items: center;
        padding: 15px 0;
        .train-book {
          width: 31px;
          height: 32px;
          padding-bottom: 29px;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .train-content {
          // flex-grow:1;
          display: flex;
          align-items: center;
          margin-left: 27px;
          font-size: 32px;
          line-height: 1.05;
          color: #333333;
          border-bottom: 1px solid #ececec;
          padding-bottom: 27px;
          p {
            width: 584px;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
          .train-arrow {
            width: 17px;
            height: 30px;
            margin-left: 32px;
            img {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
</style>
