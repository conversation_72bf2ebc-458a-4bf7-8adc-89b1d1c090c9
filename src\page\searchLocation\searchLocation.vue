<template>
    <div class="search">
        <form action="" v-on:submit.prevent="">
            <div class="searchbar" >
                <div class="searchbar-inner">
                    <div class="searchbar-inner-location" v-show="currentCity">{{currentCity}}</div>
                    <!-- <div class="searchbar-inner-location">杭州</div> -->
                    <div class="search-input-con">
                        <i class="search-input-icon"></i>
                        <input type="search" ref="search" v-model="searchValue" placeholder="输入要录入坐标的机构ID/名称/地址" class="search-input" @keyup.13="search"/>
                        <i class="search-input-icon-right" v-show="showRightIcon" @touchstart="clear"></i>
                    </div>
                </div>
            </div>
        </form>
        <div v-show="result.length > 0" class="tab-con">
            <div v-for="item in result" :key="item.orgId" class="cell-row">
                <div class="cell-row-con">
                    <div class="cell-row-orgName">
                        {{item.orgName}}
                    </div>
                    <div class="cell-row-orgId">  
                        ID:{{item.orgId}}
                    </div>
                    <div class="cell-row-orgAddress">
                        地址：{{item.orgAddress}}
                    </div>
                </div>
                <div class="checkbox " :class="item.orgId === selectedItem.orgId ? 'checkbox-checked':'checkbox-normal'"  @touchstart="clickCheckBox(item)">
                </div>
            </div>
        </div>
        <div class="empty-con" v-show="result.length == 0">
            暂无数据
        </div> 
        <modal :show="modalShow" @close="closeModal">
            <div>
                <div class="error-icon">
                </div>
                <div class="custom-modal-content-title">未找到您要的医疗机构，建议复制地址，进入外部导航系统搜索。      
                </div>
                <div class="btn-search" @click="openMap">
                    去查找
                </div>
            </div>
        </modal>
    </div>
</template>
<script>
import modal from '@/components/customModal/modal.vue'
import {queryOrganForAdd} from '@/api/sign'
import userService from '@/services/userService'
export default {
    name:'searchLocation',
    components:{
        modal
    },
    data(){
        return {
            modalShow: false,
            selectedItem:{},
            showRightIcon:false,
            searchValue:'',
            result:[],
            currentCity:'',
            currentLgt:'',
            currentLat:''
        }
    },
    watch: {
		searchValue(nv, ov){
			const _this = this;
			if (nv === '') {
				this.showRightIcon = false
			} else {
				this.showRightIcon = true
			}
		}
	},
    mounted(){
        this.getListData()
        this.getLocation()
    },
    methods:{
        getListData(){
            var that = this
            this.$loading(true,'')
            queryOrganForAdd({
                orgName:this.searchValue
            }).then(res=>{
                console.log(res)
                if(res.errorCode == 0){
                    that.result = res.data
                    if(that.result.length == 0 && that.searchValue){
                        that.modalShow = true
                    }
                }else{
                    that.$toast.center(res.value)
                }
                that.$loading(false,'')
            }).catch(e=>{
                that.$toast.center(e)
                that.$loading(false,'')
            })

        },
        // 获取当前坐标 - 已替换为新的位置服务
		async getLocation () {
			try {
				console.log('开始获取位置信息...');
				const locationResult = await userService.getUserLocation();
				console.log('位置信息获取结果:', locationResult);

				if (locationResult.success) {
					const { longitude, latitude } = locationResult.location;
					console.log('使用位置:', longitude, latitude, '来源:', locationResult.source);
					this.currentLgt = longitude;
					this.currentLat = latitude;
					this.getRegeCode(this.currentLgt, this.currentLat);
				} else {
					console.log('位置获取失败，给出默认位置');
					this.currentLgt = 120.14738921730009;
					this.currentLat = 30.244121753904395;
					this.getRegeCode(this.currentLgt, this.currentLat);
				}
			} catch (error) {
				console.error('获取位置信息异常:', error);
				// 给出默认位置
				this.currentLgt = 120.14738921730009;
				this.currentLat = 30.244121753904395;
				this.getRegeCode(this.currentLgt, this.currentLat);
			}
        },
        // 转地理编码　regeoCode
		getRegeCode(lnt,lat){
			var that = this
			AMap.plugin('AMap.Geocoder',function(){//异步加载插件
				var geocoder = new AMap.Geocoder({
				city: "全国", 
				radius: 1000 
			});
			var lnglat = [lnt,lat]
			geocoder.getAddress(lnglat, function(status, result) {
				console.log(result)
				if (status === 'complete'&&result.regeocode) {
                    that.currentCity = result.regeocode.addressComponent.city
                    that.currentCityCode = result.regeocode.addressComponent.cityCode
                    var index = that.currentCity.indexOf('市')
                    if(index != -1 ){
                        that.currentCity = that.currentCity.slice(0,index)
                    }
				}
			});
			});
        },
        openMap(){
            var _this = this
            if (window.yl.getSystemInfo().appVersion) {
                window.yl.call("mapNavigation",{longitude:_this.currentLgt,latitude:_this.currentLat},
                {
                    onSuccess:function (a) {
                        _this.modalShow = false
                    },
                    onFail:function (a) {
                        _this.modalShow = false 
                    }
                });
            }else{
                _this.$toast.center('请下载并使用杭州办事APP')
            }
        },
        clear(){
            this.searchValue = ''
        },
        search(){
            this.getListData()
            document.activeElement.blur();
        },
        clickCheckBox(item){
            this.selectedItem = Object.assign({},item)
            this.$store.commit('updateLocationItem', this.selectedItem);
            this.$router.push({
			   path:'/updatePosition/2'
		    })
        },
        closeModal(){
            this.modalShow = false
        }
    }
}
</script>
<style lang="less" scoped>
.search {
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
  .mint-search-list {
    padding-top: 0.8rem;
  }
}
.tab-con {
    margin-top: 120px;
}
.cell-row {
    padding: 20px 40px;
    font-size: 32px;
    border-bottom: 2px solid #ECECEC;
    display: flex;
    align-items: center;
    &-con {
        flex: 1;
        padding-right: 10px;
    }
    &-orgName {
        color: #000000;
        font-size: 34px;
        margin-bottom: 20px;
        line-height: 40px;
    }
    &-orgId {
        color: #333333;
        font-size: 32px;
        margin-bottom: 34px;
    }
    &-orgAddress {
        color: #777777;
        font-size: 28px;
        line-height: 40px;
    }
}

.checkbox {
    background-repeat: no-repeat;
    background-size: 100%;
    width: 38px;
    height: 38px;
}

.checkbox-checked {
    background-image: url(../../assets/img/checkbox.png);
}

.checkbox-normal {
    background-image: url(../../assets/img/checkboxNormal.png);
}

.empty-con {
  text-align: center;
  margin-top: 25%;
  font-size: 30px;
  color: #777777;
  img {
    width:400px;
  }
  p {
    color: #999999;
    font-size: 32px;
    margin-bottom: 20px;
  }
  div {
    font-size: 32px;
    width: 200px;
    margin: auto;
    background-color: #317DFF;
    padding: 20px;
    color: white;
    border-radius: 8px;
  }
}
// .mint-searchbar-inner{
//   padding: 20px 0.08rem !important; 
// }



</style>