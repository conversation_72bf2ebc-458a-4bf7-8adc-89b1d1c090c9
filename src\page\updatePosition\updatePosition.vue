<template>
<div class="s">
	<div class="s-map" ref="container" id="container"></div>
	<form action="" v-on:submit.prevent="">
		<div class="searchbar" >
			<div class="searchbar-inner">
				<div class="searchbar-inner-location" v-if="currentCity">{{currentCity}}</div>
				<!-- <div class="searchbar-inner-location">杭州</div> -->
				<div class="search-input-con">
					<i class="search-input-icon"></i>
					<input type="search" ref="search" v-model="sword" placeholder="输入小区、街道、写字楼等" class="search-input" @keyup.13="clickSearch"/>
					<i class="search-input-icon-right" v-show="showClear" @touchstart="clearWordFn"></i>
				</div>
			</div>
		</div>
	</form>
	<!-- <form action="" v-on:submit.prevent="">
		<div class="s-input">
			<div class="slocation" v-if="currentCity">{{currentCity}}</div>
			<div class="s2lt"></div>
			<div class="s2ct">
				<input type="search" placeholder="输入小区、街道、写字楼等" v-model="sword" @keyup.13="clickSearch">
				<i class="clearWord" @click="clearWordFn" v-show="showClear"></i>
			</div>
		</div>
	</form> -->
	<div class="s3"  ref="orgInfo" v-if="checkedItem.orgLongitude && checkedItem.orgLatitude ">
		<!-- <div class="s3c">
			<ul>
				<li v-for="(item, index) in resultList" :key="'li' + index" @click="updateInfo(item)">
					<div class="s3cUlt">
						<p>{{item.address}}</p>
					</div>
				</li>
			</ul>
		</div> -->
		<div class="cell-row">
			<div class="cell-row-con">
				<div class="cell-row-orgName">
					{{checkedItem.orgName}}
				</div>
				<div class="cell-row-orgAddress">
					地址：{{checkedItem.orgAddress}}
				</div>
			</div>
			<div class="checkbox " :class="checkedBox ? 'checkbox-checked':'checkbox-normal'"  @touchstart="clickCheckBox(checkedItem)">
			</div>
		</div>
		<!-- <div class="btn" @click="toggleOpen($event)" ref="listBtn"></div> -->
	</div>

	<!-- <div class="s3" v-show="showResultList" ref="list">
		<div class="s3c">
			<ul>
				<li v-for="(item, index) in resultList" :key="'li' + index">
					<div class="s3cUlt">
						<h3>{{item.orgName}}</h3>
						<p>{{item.orgAddress}}</p>
					</div>
					<div class="s3cUrt" ></div>
				</li>
			</ul>
		</div>
		<div class="btn" @click="toggleOpen($event)" ref="listBtn"></div>
	</div> -->
	<div class="relLocation" @click="backCurrent" :style="{'bottom':relLocBottom +'px'}"><img src="../../assets/img/relLocation.png" alt=""> <span>重新定位</span></div>

</div>
</template>
<script>
import currentImg from '@/assets/img/<EMAIL>'
import ydIcon from '@/assets/img/ydIcon.png'
import yyIcon from '@/assets/img/yyIcon.png'
import pointsImg from '@/assets/img/<EMAIL>'
import dirImg from '@/assets/img/<EMAIL>'
import busImg from '@/assets/img/<EMAIL>'
import carImg from '@/assets/img/<EMAIL>'
import walkImg from '@/assets/img/<EMAIL>'
import moveImg from '@/assets/img/<EMAIL>'
import html2canvas from 'html2canvas'
import {initCookie, queryOrganOnMyScreen, queryOrganByOrgName, selectOrganInfo, selectOrganInfoNoToken, queryOrganByMyCoordinate} from '../../api/check'
import userService from '@/services/userService'
export default {
    name:'updatePosition',
	data () {
		return {
			scImg:'',
			currentCity:'',
			currentCityCode:'',
			checkedBox:false,
            checkedItem:{},
			map: null,
			current: null,
			// 搜索词
			sword: '',
			// 地图上的定位点
			markerArr: [],
			// 搜索list是否显示
			showResultList: false,
			resultList: [],
			infoBoxArr: [],
			curLocLng: '',
			curLocLat: '',
			movePosition: {
				lng: '',
				lat: ''
			},
			showClear: false,
			positionPicker: null,
			fromType:0,//0 纠错 1 补充机构 2 补充机构点击查找按钮
			routerId:1,
			isNoData:false,
			isShowDetail:false,
			firstSearch:true,
			isReLocation:false,
			relLocBottom:24
		};
	},
	// watch:{
	// // 	checkedItem:{
    // //     handler:function(val,oldval){
    // //       console.log(val.orgLongitude)
    // //     },
    // //     deep:true//对象内部的属性监听，也叫深度监听
    // //   },
	// 	// checkedItem(val){
    //     //     console.log(valval)
	// 	// 	if(this.$refs.orgInfo){
	// 	// 		let height = this.$refs.orgInfo.clientHeight
	// 	// 		this.relLocBottom = height + 24
	// 	// 	}
	// 	// }
	// },
	computed: {
    getLatitude: function() {
    	return this.checkedItem.orgLatitude
    }
},
// watch: {
//      'checkedItem.orgLatitude': {
//          handler: function() {
// 			 console.log('checkedItem.orgLatitude')
//             //do something
//          },
//      }
// },
	created () {
		
	},
	mounted () {
		this.$loading(true, '');
		this.routerId = this.$route.params.id
		this.checkedItem = Object.assign({},this.$store.getters.locationItem)
		this.getLocation();
		//  this.getLocationTest();
	   
	},
	methods: {
		getReLocBottom(){
			var that = this
			this.$nextTick(()=>{
				if(that.$refs.orgInfo){
					let height = that.$refs.orgInfo.clientHeight
					that.relLocBottom = height + 24
				}
			})
		},
		getScreenImg(selectedItem){
			var _this = this
			html2canvas(this.$refs.container, {
				width: window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth,
				height: window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight,
			}).then((canvas) => {// 第一个参数是需要生成截图的元素,第二个是自己需要配置的参数,宽高等
				_this.scImg = canvas.toDataURL('image/png');
				selectedItem.bgImg = _this.scImg
				_this.updateInfo(selectedItem)
			}).catch(()=>{
				selectedItem.bgImg = ''
                _this.updateInfo(selectedItem)
			})
		},

		clickCheckBox(item){
			this.$loading(true,'')
		    this.checkedBox = !this.checkedBox
		    var selectedItem = Object.assign({},this.checkedItem)
			selectedItem.isUpdate = true
			this.getScreenImg(selectedItem)
		},
		updateInfo(selectedItem){
			this.$loading(false,'')
			this.$store.commit('updateLocationItem', selectedItem);
			this.$store.commit('updateShowTipStatus',true)
			this.$router.push({path:'/locationCorrect/'+this.routerId})
		},
		testInit (callback, param) {
			initCookie({authCode: '11111111'}).then((result) => {
				console.log('initCookie', result);
				if (result.errorCode === '0') {
					callback(param);
				}
			})
		},
		// 初始化，获取authCode，验证是否实名
		// 初始化成功后会在session里保存用户信息
		// callback 初始化成功后的操作
		initCookiefn (callback, param) {
			const _this = this;
			window.yl.call('getAuthcode', {}, {
				onSuccess: (res) => {
					console.log('获取getAuthcode:', res);
					if (res && res.errorCode === '0') {
						initCookie({authCode: res.param.authCode}).then((result) => {
							console.log('initCookie', result);
							if (result.errorCode === '0') {
								callback(param);
							} else if (result.errorCode === '10002') {
								window.yl.call('requireAuth', {}, {
									onSuccess: function (e) {
										console.log('实名成功-res', e);
										_this.initCookiefn(callback, param);
									},
									onFail: function (e) {
										window.yl.call('closeWebview');
									}
								});
							} else {
								_this.$toast.center(result.value)
							}
						});
					}
				},
				onFail: (res) => {
					console.log('获取authCode失败');
				}
			});
		},
		// 获取当前坐标 - 已替换为新的位置服务
		async getLocation () {
			try {
				console.log('开始获取位置信息...');
				const locationResult = await userService.getUserLocation();
				console.log('位置信息获取结果:', locationResult);

				if (locationResult.success) {
					const { longitude, latitude } = locationResult.location;
					console.log('使用位置:', longitude, latitude, '来源:', locationResult.source);
					this.getRegeCode(longitude, latitude, true);
					this.initMap(latitude, longitude);
				} else {
					console.log('位置获取失败，给出默认位置');
					this.getRegeCode(120.14738921730009, 30.244121753904395, true);
					this.initMap(30.244121753904395, 120.14738921730009);
					this.getLocationFallback();
				}
			} catch (error) {
				console.error('获取位置信息异常:', error);
				// 给出默认位置
				this.getRegeCode(120.14738921730009, 30.244121753904395, true);
				this.initMap(30.244121753904395, 120.14738921730009);
			}
		},

		// 转地理编码　regeoCode
		getRegeCode(lnt,lat,flag){
			var that = this
			AMap.plugin('AMap.Geocoder',function(){//异步加载插件
				var geocoder = new AMap.Geocoder({
				city: flag ? "全国" : that.currentCityCode, 
				radius: 1000 
			});
			var lnglat = [lnt,lat]
			geocoder.getAddress(lnglat, function(status, result) {
				if (status === 'complete'&&result.regeocode) {
					if(flag){
                        that.currentCity = result.regeocode.addressComponent.city
						that.currentCityCode = result.regeocode.addressComponent.cityCode
						var index = that.currentCity.indexOf('市')
						if(index != -1 ){
							that.currentCity = that.currentCity.slice(0,index)
						}
					}else{
						that.$set(that.checkedItem,'orgAreasBelongsStr',result.regeocode.addressComponent.district)
						that.$set(that.checkedItem,'orgBelongsStreetStr',result.regeocode.addressComponent.township)
					}
				}
			});
			});
		},

		getLocationTest(){
			this.$loading(true, '');
			// this.getRegeCode(120.20737,30.2585,true)
			// this.initMap(30.2585,120.20737);
		},
		// 初始化地图
		initMap (lat, lng) {
			const _this = this; 
			_this.curLocLng = lng;
			_this.curLocLat = lat;
			const AMap = window.AMap;
			_this.map = new AMap.Map(_this.$refs.container, {
				resizeEnable: true,
				center: new AMap.LngLat(lng, lat),
                zoom: 14
			});
			// 当前位置
			_this.current = new AMap.Marker({
				icon: new AMap.Icon({
					image: currentImg,
					size: new AMap.Size(24, 24), //图标大小
					imageSize: new AMap.Size(24, 24)
				}),
				map: _this.map,
				position: new AMap.LngLat(lng,lat),
				zIndex: 200
			});
			//console.log('1111' + _this.map.getCenter())
			let circle = new AMap.Circle({
				center: new AMap.LngLat(lng,lat),
				radius: 1000, //半径
				borderWeight: 3,
				strokeColor: "#317DFF", 
				strokeOpacity: 0.15,
				// strokeWeight: 6,
				strokeOpacity: 0,
				fillOpacity: 0.15,
				strokeStyle: '',
				// strokeDasharray: [10, 10], 
				// 线样式还支持 'dashed'
				fillColor: '#317DFF',
				zIndex: 50,
			});
			circle.setMap(_this.map);

            //当前范围内没有点可以点击
			_this.map.on('click', function (ev) {
			
				console.log('click=', ev);
				if(_this.isNoData){

				}
				//_this.closeSearchList();
				// _this.nearPosition({'lng': ev.lnglat.lng, 'lat': ev.lnglat.lat});
				// _this.infoBoxArr.forEach(val => {
				// 	val.close();
				// })
			})
			window.AMapUI.loadUI(['misc/PositionPicker'], function(PositionPicker) {
				// console.log('PositionPicker', PositionPicker);
				    var imgUrl = (_this.checkedItem.orgLongitude && _this.checkedItem.orgLatitude) ? (_this.checkedItem.orgType == 1  ? ydIcon : yyIcon) : moveImg
					_this.positionPicker = new PositionPicker({
						mode: 'dragMap',
						map: _this.map,
						iconStyle: {
							url: moveImg,
							size:[20,34],  //要显示的点大小，将缩放图片
							ancher:[10,17],//锚点的位置，即被size缩放之后，图片的什么位置作为选中的位置
						}
					});

					_this.positionPicker.on('success', function(pos){
						console.log('move success', pos);
						if((_this.checkedItem.orgLongitude && _this.checkedItem.orgLatitude) || (_this.isNoData && !_this.firstSearch)){ // 纠错
　　　　　　　　　　　　　　　 _this.checkedItem.orgAddress = pos.address
							_this.checkedItem.orgLongitude = pos.position.lng
							_this.checkedItem.orgLatitude = pos.position.lat
							_this.checkedItem.orgAreasBelongsStr = pos.regeocode.addressComponent.district
							_this.checkedItem.orgBelongsStreetStr = pos.regeocode.addressComponent.township
						}
						_this.getReLocBottom()
					});
					_this.positionPicker.on('fail', function(pos){
						console.log('move fail', pos);
					})  
					if(_this.checkedItem.orgLongitude && _this.checkedItem.orgLatitude && !_this.isReLocation){
						_this.positionPicker.start(new AMap.LngLat(_this.checkedItem.orgLongitude,_this.checkedItem.orgLatitude));
					}else{
						_this.isReLocation = false
						_this.positionPicker.start(new AMap.LngLat(lng,lat));
					}  
			});
			_this.map.on('dragend', function(){
				_this.firstSearch = false
			});
			if((!this.checkedItem.orgLongitude || !this.checkedItem.orgLatitude) && this.checkedItem.orgName ){
				_this.sword = _this.checkedItem.orgName
				_this.searchFn()
			}
			_this.$loading(false, '');
		},
		clickSearch(){
			this.firstSearch = false
			this.positionPicker.start(new AMap.LngLat(this.curLocLng,this.curLocLat));
			this.searchFn()
			document.activeElement.blur();
		},
		searchFn () {
			// this.$toast.center('找不到您要搜索的机构，请在地图上选择您认为的地理位置')
			var _this = this
			if(!this.sword){
			   this.$toast.center('请输入小区、街道、写字楼等')
			   return
			}
			_this.$loading(true,'')
			_this.map.remove(_this.markerArr);
			AMap.service(["AMap.PlaceSearch"], function() {
				//构造地点查询类
				var placeSearch = new AMap.PlaceSearch({ 
				    type: '医疗保健服务', // 兴趣点类别
					pageSize: 50, // 单页显示结果条数
					pageIndex: 1, // 页码
					city: _this.currentCityCode, // 兴趣点城市
					citylimit: true,  //是否强制限制在设置的城市内搜索
					// map: _this.map, // 展现结果的地图实例
					//panel: "panel", // 结果列表将在此容器中进行展示。
					autoFitView: true // 是否自动调整地图视野使绘制的 Marker点都处于视口的可见范围
				});
				var cpoint = [_this.curLocLng,_this.curLocLat]; //中心点坐标
				placeSearch.searchNearBy(_this.sword, cpoint, 1000, function(status, result) {
					console.log(result)
					_this.$loading(false,'')
					if(status === 'no_data' || (status === 'complete' && result.poiList.pois.length == 0)){
					   _this.$toast.center('附近暂无搜索的机构，请在地图上选择您认为的地理位置')
					   _this.isNoData = true
					   return
					}
					_this.isNoData = false
					
					var pois = result.poiList.pois;
					for(var i = 0; i < pois.length; i++){
						var poi = pois[i];
						// var marker = [];
						var poiType = poi.type
						console.log(poiType)
						var isyd = poiType.includes('医药保健销售店')
						_this.markerArr[i] = new AMap.Marker({
							icon: new AMap.Icon({
								image:isyd ? ydIcon : yyIcon ,
								size: new AMap.Size(30, 35),  //图标大小
								imageSize: new AMap.Size(30, 35)
							}),
							offset: new AMap.Pixel(-15,-35),
							position: poi.location,   // 经纬度对象，也可以是经纬度构成的一维数组[116.39, 39.9]
							title: poi.name
						});
						// _this.markerArr[i]
						// 将创建的点标记添加到已有的地图实例：
						_this.map.add(_this.markerArr[i]);
						//console.log('11111')
						//console.log(_this.markerArr[i])
						// _this.markerArr[i].setIcon(new AMap.Icon({
						// 	image: pointsImg,
						// 	size: new AMap.Size(33, 42),  //图标大小
						// 	imageSize: new AMap.Size(33, 42)
						// }));
						AMap.event.addListener(_this.markerArr[i], 'click', function (e) {
							
							//
                       
                        var currentImg = ''
						for(var index = 0; index < pois.length; index++){
							var poi = pois[index]
							var poiType = poi.type
							//console.log(poiType)
							var isyd = poiType.includes('药店') || poiType.includes('药房')
							var size = ''
							var offset = ''
							var img = ''
							var zIndex = 100
							if(e.target == _this.markerArr[index]){
								console.log('poi11111')
							   console.log(poi)
							  
							  size =  new AMap.Size(51, 59.5)
							  offset = new AMap.Pixel(-25.5,-59.5)
							  img = isyd ? ydIcon : yyIcon
							  zIndex= 50
							  
							  _this.$set(_this.checkedItem,'orgLongitude',poi.location.lng)
							  _this.$set(_this.checkedItem,'orgLatitude',poi.location.lat)
							  _this.$set(_this.checkedItem,'orgAddress',poi.address)
							   _this.getReLocBottom()
							// _this.getRegeCode(poi.location.lng,poi.location.lat,false)
							//   _this.$set(_this.checkedItem,'orgAreasBelongsStr',reg.regeocode.addressComponent.district)
							//   _this.$set(_this.checkedItem,'orgBelongsStreetStr',reg.regeocode.addressComponent.township)
							}else {
							  size =  new AMap.Size(30 ,35)
							  offset = new AMap.Pixel(-15,-35)
							  img =  isyd ? ydIcon : yyIcon
							}
							
							_this.markerArr[index].setIcon(new AMap.Icon({
								image: img,
								size: size,  //图标大小
								imageSize: size,
								zIndex:zIndex
							}));
							_this.markerArr[index].setOffset(offset)
						}
					})
					}
					_this.map.setFitView();
					_this.$loading(false,'')
					
				});
			});
			// this.positionPicker.start(new AMap.LngLat(120.21114,30.262537));
			// if (this.sword !== '') {
			// 	this.openSearchList();
			// 	this.queryOrganByOrgNameFn(this.sword.trim());
			// } else {
			// 	this.$toast.center('请输入关键词')
			// }
			// this.infoBoxArr.forEach(val => {
			// 	val.close();
			// })
		},
		judeType(poi){
			var poiType = poi.type
			return poiType.includes('药店') || poiType.includes('药房')
		},
		
		clearWordFn () {
			this.sword = ''
		},
		backCurrent () {
			this.isReLocation = true
			this.getLocation();
			// this.getLocationTest()
		},
		nearPosition (obj) {
			const _this = this;
			const param ={
				'currentLongitude': obj.lng,
				'currentLatitude': obj.lat
			}
			queryOrganByMyCoordinate(param).then(res => {
				console.log('queryOrganByMyCoordinate=', res);
				if (res.errorCode === '0') {
					_this.drawMarker(res.data);
				} else if (res.errorCode === '1003'){
					// console.log('1003');
					_this.testInit(_this.nearPosition, {'lng': obj.lng, 'lat': obj.lat});
					// _this.initCookiefn(_this.nearPosition, '');
				} else {
					_this.$toast.center(res.value);
				}
			}).catch(error => {
				_this.$toast.center('接口异常');
			})
		},
		// 精确搜索，只绘制搜索选中的点
		queryOrganByOrgNameFn (v) {
			const _this = this;
			const param = {
				'orgName': v
			}
			queryOrganByOrgName(param).then(res => {
				console.log('queryOrganByOrgName==', res);
				if (res.errorCode === '0') {
					if (res.data && res.data.length > 0) {
						// _this.drawMarker(res.data);
						_this.showResultList = true;
						_this.resultList = res.data;
					} else {
						_this.map.remove(_this.markerArr);
						_this.showResultList = false;
						_this.resultList = [];
						_this.$toast.center('查询无结果')
					}
				} else if (res.errorCode === '1003'){
					_this.initCookiefn(_this.queryOrganByOrgNameFn, v)
					// _this.testInit(_this.queryOrganByOrgNameFn, v)
				} else {
					_this.$toast.center(res.value);
				}
			})
		},
		// 在地图上定位点
		drawMarker (points) {
			const _this = this;
			console.log('drawMarker', points);
			let pointArr = [];
			const AMap = window.AMap;
			_this.map.remove(_this.markerArr);
			// 路线
			points.forEach((el, index) => {
				pointArr[index] = new AMap.LngLat(el.orgLongitude, el.orgLatitude);
				_this.markerArr[index] = new AMap.Marker({
						icon: new AMap.Icon({
								image: pointsImg,
								size: new AMap.Size(42, 42),  //图标大小
								imageSize: new AMap.Size(42, 42)
						}),
						offset: new AMap.Pixel(-7,-19),
						map: _this.map,
						position: pointArr[index],
						zIndex: 200
				});
				// 自定义窗体
				_this.infoBoxArr[index] = new AMap.InfoWindow({
						isCustom: true //使用自定义窗体
				});
				// 当信息窗关闭时，重置图标
				_this.infoBoxArr[index].on('close', function(){
					_this.markerArr[index].setIcon(new AMap.Icon({
							image: pointsImg,
							size: new AMap.Size(15, 19),  //图标大小
							imageSize: new AMap.Size(15, 19)
						}));
					_this.markerArr[index].setOffset(new AMap.Pixel(-7,-19));
				})
				//绑定事件一次
				_this.markerArr[index].evtFlag = true
				_this.markerArr[index].i1evtFlag = true
				_this.markerArr[index].i2evtFlag = true
				_this.markerArr[index].i3evtFlag = true
				// marker点击事件
        AMap.event.addListener(_this.markerArr[index], 'click', function () {
					// _this.map.setCenter(_this.markerArr[index].getPosition());
					_this.transfer && _this.transfer.clear();
					_this.driving && _this.driving.clear();
					_this.walking && _this.walking.clear();
					_this.closeResList();
					const param = {
						'orgId': el.orgId,
						'currentLongitude': _this.curLocLng,
						'currentLatitude': _this.curLocLat
					}
					selectOrganInfo(param).then(organInfoRes => {
						console.log('marker click selectOrganInfo=', organInfoRes);
						_this.markerArr[index].evtFlag = true
						_this.markerArr[index].i1evtFlag = true
						_this.markerArr[index].i2evtFlag = true
						_this.markerArr[index].i3evtFlag = true
						if (organInfoRes.errorCode === '0') {
							_this.clickCurrentPointFn (el.orgId, organInfoRes.data, false, index);
						} else if (organInfoRes.errorCode === '1003') {
							// 游客
							selectOrganInfoNoToken(param).then(yRes => {
								console.log('游客 click selectOrganInfoNoToken=', yRes);
								if (yRes.errorCode === '0') {
									_this.clickCurrentPointFn (el.orgId, yRes.data, true, index);
								} else {
									_this.$toast.center(yRes.value)
								}
							})
						} else {
							_this.$toast.center(organInfoRes.value)
						}
					})
				});
				// 信息框open事件
				_this.infoBoxArr[index].on('open', function (el) {
					// 信息框里的事件
					_this.showImgEvt(index);
					_this.img1Evt(index);
					_this.img2Evt(index);
					_this.img3Evt(index);
				})
				// over
			});
		},
		// 根据经纬度开启详情
		pointDetail (item) {
			const _this=this;
			_this.$loading(true, '');
			_this.closeResList();
			// 停止拖拽选点
			_this.positionPicker.stop();
			if (_this.curLocLng && _this.curLocLat) {
				const param = {
					'orgId': item.orgId,
					'currentLongitude': _this.curLocLng,
					'currentLatitude': _this.curLocLat
				}
				selectOrganInfo(param).then(res => {
					console.log('search list click selectOrganInfo=', res);
					_this.$loading(false, '');
					if (res.errorCode === '0') {
						_this.enterCurrentPointFn(item, res.data, false);
					} else if (res.errorCode === '1003') {
						// 游客
						_this.$loading(true, '');
						selectOrganInfoNoToken(param).then(yRes => {
							console.log('游客 selectOrganInfoNoToken=', yRes);
							_this.$loading(false, '');
							if (yRes.errorCode === '0') {	
								_this.enterCurrentPointFn(item, yRes.data, true);
							} else {
								_this.$toast.center(yRes.value)
							}
						})
					} else {
						_this.$toast.center(res.value)
					}
				})
			} else {
				_this.$toast.center('无法定位')
			}
		},
		// 结果list
		closeResList () {
			this.showResultList =false
		},
		showImgEvt (index) {
			const _this = this;
			// console.log('_this.markerArr[index].evtFlag', _this.markerArr[index].evtFlag);
			if (_this.markerArr[index].evtFlag) {
				_this.markerArr[index].evtFlag = false;
				document.querySelector('#showImg'+index).addEventListener('click', function(){
					let itemList = document.querySelector('.mapSCs2');
					if (itemList.classList.contains('mapSCs2act')) {
						itemList.classList.remove('mapSCs2act');
					} else {
						itemList.classList.add('mapSCs2act');
					}
				}, false)
			}
		},
		// 驾车
		img1Evt (index) {
			const _this = this;
			const AMap = window.AMap;
			if (_this.markerArr[index].i1evtFlag) {
				_this.markerArr[index].i1evtFlag = false;
				document.querySelector('#imga'+index).addEventListener('click', function(el){
					console.log('driving click');
					const evt = this;
					_this.transfer && _this.transfer.clear();
					_this.walking && _this.walking.clear();
					_this.driving.search(new AMap.LngLat(_this.curLocLng, _this.curLocLat), new AMap.LngLat(evt.dataset.lng, evt.dataset.lat));
				}, false)
			}
		},
		// 公交
		img2Evt (index) {
			const _this = this;
			const AMap = window.AMap;
			if (_this.markerArr[index].i2evtFlag) {
				_this.markerArr[index].i2evtFlag = false;
				document.querySelector('#imgb'+index).addEventListener('click', function(el){
					const evt = this;
					_this.walking && _this.walking.clear();
					_this.driving && _this.driving.clear();
					var tDistance = _this._GetDistance(evt.dataset.lat, evt.dataset.lng, _this.curLocLat, _this.curLocLng)
					if (tDistance > 0.4) {
						_this.transfer.search(new AMap.LngLat(_this.curLocLng, _this.curLocLat), new AMap.LngLat(evt.dataset.lng, evt.dataset.lat));
					} else {
						_this.$toast.center('距离太近，请步行');
					}
				}, false)
			}
		},
		// 步行
		img3Evt (index) {
			const _this = this;
			const AMap = window.AMap;
			if (_this.markerArr[index].i3evtFlag) {
				_this.markerArr[index].i3evtFlag = false;
				document.querySelector('#imgc'+index).addEventListener('click', function(el){
					const evt = this;
					_this.transfer && _this.transfer.clear();
					_this.driving && _this.driving.clear();
					_this.walking.search(new AMap.LngLat(_this.curLocLng, _this.curLocLat), new AMap.LngLat(evt.dataset.lng, evt.dataset.lat));
				}, false)
			}
		},
		// checkNum:[true | false] 游客没有检查次数
		enterCurrentPointFn (item, el, checkNum) {
			const _this = this;
			const AMap = window.AMap;
			var onePointArr = [];
			onePointArr.push(item)
			_this.drawMarker(onePointArr);	
			_this.infoBoxArr[0].setContent(`
			<div class="mapSC">
				<div class="mapSCp">
					<h2>${el.orgName}<span style="display:${el.orgLevel ? 'inline-block' : 'none' }">${el.orgLevel? el.orgLevel : ''}</span></h2>
					<p class="p1">${el.orgAddress}</p>
					<p class="p2">${parseInt(el.distance) >1000? parseInt(parseInt(el.distance)/100)/10 +'km' : parseInt(el.distance)+'m'}
						<span style="display:${el.areasName ? 'inline-block' : 'none' }">|</span>${el.areasName? el.areasName: ''}
					</p>
					<p class="p3" style="display:${checkNum ? 'none' : 'block'};">历史检查：${el.historyCheckNum}次</p>
				</div>
				<div class="mapSCs">
					<div class="mapSCs1" id="showImg0"><img src="${dirImg}"/></div>
					<div class="mapSCs2">
						<div class="img img1" data-lng="${el.orgLongitude}" data-lat="${el.orgLatitude}" id="imga0"><img src="${carImg}"/></div>
						<div class="img img2" data-lng="${el.orgLongitude}" data-lat="${el.orgLatitude}" id="imgb0"><img src="${busImg}"/></div>
						<div class="img img3" data-lng="${el.orgLongitude}" data-lat="${el.orgLatitude}" id="imgc0"><img src="${walkImg}"/></div>
					</div>
				</div>
			</div>
			`);
			
			// 当前定位图标变大
			// _this.markerArr[0].setIcon(new AMap.Icon({
			// 	image: pointsImg,
			// 	size: new AMap.Size(33, 42),  //图标大小
			// 	imageSize: new AMap.Size(33, 42)
			// }));
			// _this.markerArr[0].setOffset(new AMap.Pixel(-16,-42));
			_this.infoBoxArr[0].open(_this.map, new AMap.LngLat(item.orgLongitude, item.orgLatitude));
			_this.map.setCenter(new AMap.LngLat(item.orgLongitude, item.orgLatitude));
			// 关闭弹窗
			_this.closeResList();
		},
		clickCurrentPointFn (orgId, info, checkNum, index) {
			const _this = this;
			const AMap = window.AMap;
			_this.infoBoxArr[index].setContent(`
			<div class="mapSC">
				<div class="mapSCp">
					<h2>${info.orgName}<span style="display:${info.orgLevel ? 'inline-block' : 'none' }">${info.orgLevel? info.orgLevel : ''}</span></h2>
					<p class="p1">${info.orgAddress}</p>
					<p class="p2">${parseInt(info.distance) >1000? parseInt(parseInt(info.distance)/100)/10 +'km' : parseInt(info.distance)+'m'}
						<span style="display:${info.areasName ? 'inline-block' : 'none' }">|</span>${info.areasName? info.areasName: ''}
					</p>
					<p class="p3" style="display:${checkNum ? 'none' : 'block'};">历史检查：${info.historyCheckNum}次</p>
				</div>
				<div class="mapSCs">
					<div class="mapSCs1" id="showImg${index}"><img src="${dirImg}"/></div>
					<div class="mapSCs2">
						<div class="img img1" data-lng="${info.orgLongitude}" data-lat="${info.orgLatitude}" id="imga${index}"><img src="${carImg}"/></div>
						<div class="img img2" data-lng="${info.orgLongitude}" data-lat="${info.orgLatitude}" id="imgb${index}"><img src="${busImg}"/></div>
						<div class="img img3" data-lng="${info.orgLongitude}" data-lat="${info.orgLatitude}" id="imgc${index}"><img src="${walkImg}"/></div>
					</div>
				</div>
			</div>
			`);
			
			_this.infoBoxArr[index].open(_this.map, _this.markerArr[index].getPosition());
			_this.markerArr[index].setIcon(new AMap.Icon({
					image: pointsImg,
					size: new AMap.Size(33, 42),  //图标大小
					imageSize: new AMap.Size(33, 42)
				}));
			_this.markerArr[index].setOffset(new AMap.Pixel(-16,-42));
			// 信息框里的事件
			_this.showImgEvt(index);
			_this.img1Evt(index);
			_this.img2Evt(index);
			_this.img3Evt(index);
		},
		toggleOpen () {
			const listBox = this.$refs.list;
			const listBtn = this.$refs.listBtn;
			// listBtn
			if (listBox.classList.contains('s3Act')) {
				listBox.classList.remove('s3Act');
				listBtn.innerHTML='';
			} else {
				listBox.classList.add('s3Act');
				listBtn.innerHTML='点击查看更多结果';
			}
		},
		openSearchList () {
			const listBox = this.$refs.list;
			const listBtn = this.$refs.listBtn;
			// listBtn
			if (listBox.classList.contains('s3Act')) {
				listBox.classList.remove('s3Act');
				listBtn.innerHTML='';
			}
		},
		// 关闭查询列表
		closeSearchList () {
			const listBox = this.$refs.list;
			const listBtn = this.$refs.listBtn;
			// listBtn
			if (!listBox.classList.contains('s3Act')) {
				listBox.classList.add('s3Act');
				listBtn.innerHTML='点击查看更多结果';
			}
			this.infoBoxArr.forEach(val => {
				val.close();
			})
		},
		_throttle (fn, context, delay, text) {
			clearTimeout(fn.timeoutId);
			fn.timeoutId = setTimeout(function() {
					fn.call(context, text);
			}, delay);
		},
		_getRad(d) {
    	return d * Math.PI / 180.0;
		},
		_GetDistance(lat1, lng1, lat2, lng2) {
			const _this = this;
			var radLat1 = _this._getRad(lat1);
			var radLat2 = _this._getRad(lat2);
			var a = radLat1 - radLat2;
			var b = _this._getRad(lng1) - _this._getRad(lng2);
			var s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) + Math.cos(radLat1) * Math.cos(radLat2) *
					Math.pow(Math.sin(b / 2), 2)));
			s = s * 6378.137;
			s = Math.round(s * 10000) / 10000;
			return s;
		}
	},
	directives: {
		'mtfocus' (el, binding, vnode) {
			el.onfocus = function () {
				let itemList = document.querySelector('.mapSCs2');
				if (itemList && itemList.classList.contains('mapSCs2act')) {
					itemList.classList.remove('mapSCs2act');
				}
			}
		}
	},
	components: {
	},
	watch: {
		sword(nv, ov){
			const _this = this;
			if (nv === '') {
				this.showClear = false
			} else {
				this.showClear = true
			}
		},
	// 	'checkedItem.orgLatitude': {
    //      handler: function() {
	// 		 console
	// 		 if(this.$refs.orgInfo){
	// 			 let height = this.$refs.orgInfo.clientHeight
	// 			 this.relLocBottom = height + 24
	// 		 }
    //         //do something
    //      },
    //  }
	}
};
</script>
<style lang="less" scoped>
@import '../../assets/css/mixin.less';
.s{
	width: 100%;
	height: 100%;
	position: relative;
	line-height: 1.5;
	.s-map{
		width: 100%;
		height: 100%;
	}
	.s-input{
		position: absolute;
		left: 50%;
		top:30px;
		transform: translateX(-50%);
		width: 678px;
		height: 88px;
		padding-left: 12px;
		background-color: #fff;
		border-radius:44px;
		display: flex;
		overflow: hidden;
		box-shadow:0px 3px 13px 0px rgba(0, 0, 0, 0.18);
		z-index: 100;
		align-items: center;
		.slocation {
			font-size: 32px;
			color: #333333;
			flex: 0 0 80px;
			border-right: 2px solid #E4E3E3;
			margin-left: 20px
		}
		.s2lt{
			flex: 0 0 54px;
			height: 88px;
			background: url(../../assets/img/<EMAIL>) no-repeat center center;
			background-size: 32px 33px;
		}
		.s2rt{
			flex: 0 0 156px;
			height: 88px;
			.s2rt-btn{
				height: 60px;
				padding: 14px 17px;
				span{
					display: block;
					width: 100%;
					height: 100%;
					line-height: 60px;
					text-align: center;
					background: #317DFF;
					border-radius: 30px;
					font-size: 32px;
					font-weight: 500;
					color: #fff;
				}
			}
		}
		.s2ct{
			flex: auto;
			position: relative;
			input{
				border: none;
				font-size: 30px;
				width: 100%;
				height: 100%;
				outline: none;
				line-height: 1.5;
				font-weight: 500;
				padding: 0;
				margin: 0;
				&::placeholder{
					color: #777;
				}
			}
			.clearWord{
				position: absolute;
				width: 32px;
				height: 32px;
				right: 20px;
				top: 50%;
				margin-top: -16px;
				background: url(../../assets/img/<EMAIL>) no-repeat center center;
				background-size: 32px 32px;
			}
		}
	}
	.s3{
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		max-height: 60%;
		padding: 30px 0;
		background: #fff;
		z-index: 1000;
		transition: all 0.3s linear;
		.btn{
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 60px;
			line-height: 60px;
			font-size: 32px;
			color: #666;
			text-align: center;
		}
		.s3c{
			width: 100%;
			height: 100%;
			z-index: 12;
			overflow: auto;
			-webkit-overflow-scrolling: touch;
			ul{
				li{
					padding: 0 20px 0 30px;
					display: flex;
					position: relative;
					&::after{
						.setBottomLine(#ECECEC);
					}
					.s3cUlt{
						flex: auto;
						max-width: 616px;
						overflow: hidden;
						padding: 23px 0 25px 0;
						h3{
							font-size: 34px;
							color: #333;
							padding-bottom: 12px;
							font-weight: bold;
						}
						P{
							font-size: 28px;
							color: #333;
						}
					}
					.s3cUrt{
						flex: 0 0 84px;
						background: url(../../assets/img/<EMAIL>) no-repeat right center;
						background-size: 84px 84px;
					}
				}
			}
		}
	}
	.s3Act{
		height: 0;
		transition: all 0.3s linear;
	}
	.relLocation{
		position:absolute;
		bottom:400px;
		left:27px;
		display:flex;
		justify-content: center;
		align-items: center;
		background:#fff;
		box-shadow:0px 2px 8px 1px rgba(179,180,189,0.25);
		width:204px;
		height:56px;
		// border:1px solid #ddd;
		border-radius:30px;
		color:#333333;
		font-size:30px;
		img{
			width:32px;
			height:32px;
			margin-right:10px;
		}
	}
	.checkbox {
		background-repeat: no-repeat;
		background-size: 100%;
		width: 38px;
		height: 38px;
	}

	.checkbox-checked {
		background-image: url(../../assets/img/checkbox.png);
	}

	.checkbox-normal {
		background-image: url(../../assets/img/checkboxNormal.png);
	}
	.cell-row {
		padding: 20px 40px;
		margin-bottom: 40px;
		font-size: 32px;
		border-bottom: 2px solid #ECECEC;
		display: flex;
		align-items: center;
		&-con {
			flex: 1;
			padding-right: 10px;
		}
		&-orgName {
			color: #000000;
			font-size: 34px;
			margin-bottom: 20px;
			line-height: 40px;
		}
		&-orgId {
			color: #333333;
			font-size: 32px;
			margin-bottom: 34px;
		}
		&-orgAddress {
			color: #777777;
			font-size: 28px;
			line-height: 40px;
		}
	}
}

.searchbar {
    background-color: #F6F6F7;
    display: flex;
	align-items: center;
	position: absolute;
    left: 50%;
    top: 60px;
    transform: translateX(-50%);
    width: calc(100% - 60px);
    background-color: #fff;
    border-radius: 44px;
    display: flex;
    overflow: hidden;
    box-shadow: 0px 3px 13px 0px rgba(0, 0, 0, 0.18);
    z-index: 100;
	left: 50%;
	height: auto;
	padding: inherit;
}
.searchbar-inner {
    background: white;
    display: flex;
    flex: 1;
    align-items: center;
    border-radius: 44px;
    padding: 14px 0px 14px 15px;
}
.searchbar-inner-location {
    color:#333333;
    font-size: 32px;
    flex: 0 0 80px;
    border-right: 2px solid #E4E3E3;
    margin-left: 20px;
    margin-right: 10px;
    
}
.search-input-con {
    flex: 1;
    display: flex;
    align-items: center;
}
.search-input {
    color: #777777;
    font-size: 30px;
    box-sizing: border-box;
    width: 100%;
    height: 100%;
    border: none;
    // border-radius: 4px;
    padding: 10px;
    border-radius: 44px;
    &:focus {
    outline: none;
}
}
.search-input-icon {
    width: 32px;
    height: 33px;
    background-image: url(../../assets/img/<EMAIL>);
    background-repeat: no-repeat;
    background-size: 100%;
    margin-left: 10px;
}
.search-input-icon-right {
    width: 32px;
    height: 32px;
    background: url(../../assets/img/<EMAIL>) no-repeat center center;
    background-size: 32px 32px;
    padding-right: 10px;
    margin-right: 20px;
}
.search {
    overflow: auto;
    height: 100%;
    -webkit-overflow-scrolling: touch;
  .mint-search-list {
    padding-top: 0.8rem;
  }
}

</style>
